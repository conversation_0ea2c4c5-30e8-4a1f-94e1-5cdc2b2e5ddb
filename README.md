# Simple WGPU 2D Game Engine

A high-performance 2D game engine built with Rust, WGPU, and Winit, designed for simplicity and performance.

## Project Structure

```
simple_wgpu/
├── engine/                    # Dynamic library crate (cdylib + rlib)
│   ├── src/
│   │   ├── lib.rs            # Main library entry point
│   │   ├── core/             # Core engine initialization
│   │   ├── window/           # Window management (winit)
│   │   ├── settings/         # Engine settings management
│   │   ├── asset/            # Asset loading and management
│   │   ├── render/           # Rendering system (wgpu)
│   │   └── utils/            # Utility functions
│   └── Cargo.toml
├── game/                     # Example game executable
│   ├── src/
│   │   └── main.rs
│   └── Cargo.toml
└── assets/                   # Asset directory
    ├── sprites/              # Sprite images
    └── determinationmonoweb-webfont.ttf
```

## Features Implemented

### ✅ Core Infrastructure
- **Engine Initialization**: Complete wgpu device, adapter, and queue setup
- **Global State Management**: Thread-safe engine state with proper error handling
- **Dynamic Library Architecture**: Engine compiled as both cdylib and rlib

### ✅ Settings System
- **Runtime Configuration**: VSync, fullscreen, FPS limits, resolution
- **Persistent Settings**: Automatic save/load from `engine_config.toml`
- **Hot-swappable Settings**: Change settings without restart
- **Supported Settings**:
  - VSync: On/Off/Adaptive
  - Fullscreen: Windowed/Borderless/Exclusive
  - FPS Limit: Uncapped/30/60/120/Custom
  - Resolution: Width x Height

### ✅ Window Management
- **Cross-platform Windowing**: Using winit for Linux/Windows support
- **Surface Creation**: Proper wgpu surface setup with format selection
- **Window Resizing**: Runtime resize handling with surface reconfiguration
- **Window Controls**: Title setting, size queries

### ✅ Asset System
- **Image Loading**: Support for PNG, JPG, BMP, WEBP formats
- **Font Loading**: TTF font support with fontdue
- **Asset Organization**: Automatic assets folder detection
- **GPU Texture Upload**: Efficient texture creation and management
- **Asset Validation**: Proper error handling for missing files

### ✅ Rendering Foundation
- **Shader System**: WGSL shaders for sprite rendering
- **Vertex/Index Buffers**: Optimized quad rendering setup
- **Instance Rendering**: Support for batched sprite rendering
- **Render Pipeline**: Complete graphics pipeline setup
- **Command Batching**: Frame-based command collection
- **Delta Time & FPS**: Accurate timing measurements

### ✅ API Design
- **Simple API**: Hide wgpu/winit complexity behind clean functions
- **Error Handling**: Comprehensive error types with proper propagation
- **Normalized Coordinates**: 0.0-1.0 coordinate system
- **Z-ordering**: Depth-based sprite layering (0.0=front, 1.0=back)

## API Reference

### Core Module
```rust
core::init()                    // Initialize engine
core::shutdown()                // Shutdown and cleanup
```

### Window Module
```rust
window::new(title, id)          // Create window
window::handle_resize(w, h)     // Handle resize events
window::get_size()              // Get current window size
window::set_title(title)        // Set window title
```

### Settings Module
```rust
settings::set(vsync, fullscreen, fps_limit, res_x, res_y)  // Apply settings
settings::get()                 // Get current settings
```

### Asset Module
```rust
asset::load_sprite(filename, sprite_name)    // Load sprite from assets/sprites/
asset::load_font(filename, font_name)        // Load font from assets/
asset::has_sprite(sprite_name)               // Check if sprite exists
asset::has_font(font_name)                   // Check if font exists
```

### Render Module
```rust
render::start()                 // Begin frame rendering
render::stop()                  // End frame and present
render::clearcolor()            // Clear color buffer
render::cleardepth()            // Clear depth buffer
render::sprite(name, x, y, z, scale, rotation)  // Draw sprite
render::text(text, font, x, y, z, scale, rotation)  // Draw text
render::get_delta_time()        // Get frame delta time
render::get_fps()               // Get current FPS
```

## Coordinate System

- **Normalized Coordinates**: All positions use 0.0-1.0 range
- **Origin**: (0,0) = top-left corner, (1,1) = bottom-right corner
- **Z-ordering**: 0.0 = front (drawn last), 1.0 = back (drawn first)
- **Scale**: 1.0 = normal size, 2.0 = double size, 0.5 = half size
- **Rotation**: Radians, positive = clockwise

## Usage Example

```rust
use simple_wgpu_engine::*;

fn main() -> std::result::Result<(), Box<dyn std::error::Error>> {
    // Initialize engine
    core::init()?;
    
    // Create window
    window::new("My Game", 1)?;
    
    // Configure settings
    settings::set(
        settings::VsyncType::On,
        settings::FullscreenType::Windowed,
        settings::FpsLimit::Fps60,
        1280, 720
    )?;
    
    // Load assets
    asset::load_sprite("player.png", "player")?;
    asset::load_font("font.ttf", "ui_font")?;
    
    // Game loop
    loop {
        render::start()?;
        render::clearcolor()?;
        
        // Draw sprites and text
        render::sprite("player", 0.5, 0.5, 0.0, 1.0, 0.0)?;
        render::text("Hello World!", "ui_font", 0.1, 0.1, 0.0, 1.0, 0.0)?;
        
        render::stop()?;
    }
}
```

## Building and Running

```bash
# Build the entire workspace
cargo build

# Run the example game
cargo run --bin simple_wgpu_game

# Build in release mode
cargo build --release
```

## Dependencies

- **wgpu**: Graphics API abstraction (Vulkan/DX12/Metal)
- **winit**: Cross-platform windowing
- **image**: Image loading and processing
- **fontdue**: TTF font rendering
- **glam**: Mathematics library
- **bytemuck**: Safe transmutation for GPU data
- **serde + toml**: Settings serialization
- **env_logger**: Development logging

## Current Status

The engine foundation is complete and functional. The basic architecture supports:
- Engine initialization and shutdown
- Window creation and management
- Settings persistence and hot-swapping
- Asset loading (sprites and fonts)
- Basic rendering pipeline setup
- Frame timing and FPS measurement

## Next Steps for Full Implementation

1. **Complete Rendering System**:
   - Implement actual sprite and text rendering in render passes
   - Add texture atlas generation and management
   - Implement batch rendering optimization

2. **Event Loop Integration**:
   - Proper winit event loop handling
   - Input system (keyboard, mouse)
   - Window event processing

3. **Advanced Features**:
   - Hot-reloading for development mode
   - Texture atlas optimization
   - Multi-window support
   - Audio system integration points

The current implementation provides a solid foundation that follows the architectural plan and demonstrates all the core concepts working together.
