[package]
name = "simple_wgpu_engine"
version = "0.1.0"
edition = "2021"

[lib]
crate-type = ["cdylib", "rlib"]

[dependencies]
wgpu = { version = "0.19", features = ["wgsl"] }
winit = "0.29"
image = "0.24"
bytemuck = { version = "1.14", features = ["derive"] }
glam = "0.25"
pollster = "0.3"
env_logger = "0.10"
serde = { version = "1.0", features = ["derive"] }
fontdue = "0.7"
notify = "6.1"
rectangle-pack = "0.4"
log = "0.4"
anyhow = "1.0"
thiserror = "1.0"
toml = "0.8"
