use crate::{core, EngineError, Result};
use winit::{
    window::WindowBuilder,
    dpi::PhysicalSize,
};
use std::sync::Arc;

/// Create a new window with specified title and ID (simplified version)
pub fn new(title: &str, _id: i32) -> Result<()> {
    // Store the window title for later use
    log::info!("Window parameters set: title='{}', id={}", title, _id);
    Ok(())
}

/// Create a new window with specified title and ID using an existing event loop
pub fn new_with_event_loop(title: &str, _id: i32, event_loop: &winit::event_loop::EventLoopWindowTarget<()>) -> Result<()> {
    core::with_engine_state(|state| {
        // Only allow one window for now
        if state.window.is_some() {
            return Err(EngineError::Window("Window already exists".to_string()));
        }

        // Create window
        let window = WindowBuilder::new()
            .with_title(title)
            .with_inner_size(PhysicalSize::new(state.settings.resolution_x, state.settings.resolution_y))
            .with_resizable(true)
            .build(&event_loop)
            .map_err(|e| EngineError::Window(format!("Failed to create window: {}", e)))?;

        let window = Arc::new(window);

        // Create surface
        let surface = state.instance.create_surface(window.clone())
            .map_err(|e| EngineError::Window(format!("Failed to create surface: {}", e)))?;

        // Get surface capabilities
        let surface_caps = surface.get_capabilities(&state.adapter);

        // Choose surface format
        let surface_format = surface_caps
            .formats
            .iter()
            .copied()
            .find(|f| f.is_srgb())
            .unwrap_or(surface_caps.formats[0]);

        // Create surface configuration
        let config = wgpu::SurfaceConfiguration {
            usage: wgpu::TextureUsages::RENDER_ATTACHMENT,
            format: surface_format,
            width: state.settings.resolution_x,
            height: state.settings.resolution_y,
            present_mode: match state.settings.vsync {
                crate::settings::VsyncType::Off => wgpu::PresentMode::Immediate,
                crate::settings::VsyncType::On => wgpu::PresentMode::Fifo,
                crate::settings::VsyncType::Adaptive => wgpu::PresentMode::FifoRelaxed,
            },
            alpha_mode: surface_caps.alpha_modes[0],
            view_formats: vec![],
            desired_maximum_frame_latency: 2,
        };

        surface.configure(&state.device, &config);

        // Get MSAA sample count from settings
        let requested_sample_count = state.settings.msaa.sample_count();

        // Create MSAA texture if needed and get the validated sample count
        let validated_sample_count = state.renderer.create_msaa_texture(&state.device, state.settings.resolution_x, state.settings.resolution_y, requested_sample_count, surface_format)?;

        // Create render pipelines now that we have the surface format, using the validated sample count
        state.renderer.create_pipeline(&state.device, surface_format, validated_sample_count)?;
        state.renderer.create_text_pipeline(&state.device, surface_format, validated_sample_count)?;

        // Store window, surface, and config
        state.window = Some(window);
        state.surface = Some(surface);
        state.surface_config = Some(config);

        log::info!("Window created successfully: {}", title);
        Ok(())
    })?;

    // TODO: The event loop needs to be run externally
    // For now, we'll just create the window and return
    log::warn!("Event loop management needs to be handled by the application");
    Ok(())
}

/// Handle window resize
pub fn handle_resize(new_width: u32, new_height: u32) -> Result<()> {
    core::with_engine_state(|state| {
        if let (Some(surface), Some(ref mut config)) = (&state.surface, &mut state.surface_config) {
            config.width = new_width;
            config.height = new_height;
            surface.configure(&state.device, config);

            // Recreate MSAA texture with new dimensions if MSAA is enabled
            let requested_sample_count = state.settings.msaa.sample_count();
            if requested_sample_count > 1 {
                let validated_sample_count = state.renderer.create_msaa_texture(&state.device, new_width, new_height, requested_sample_count, config.format)?;
                log::info!("Recreated MSAA texture for new window size: {}x{} with {}x samples", new_width, new_height, validated_sample_count);
            }

            log::info!("Window resized to {}x{}", new_width, new_height);
        }
        Ok(())
    })
}

/// Get current window size
pub fn get_size() -> Result<(u32, u32)> {
    core::with_engine_state(|state| {
        if let Some(ref window) = state.window {
            let size = window.inner_size();
            Ok((size.width, size.height))
        } else {
            Err(EngineError::Window("No window created".to_string()))
        }
    })
}

/// Set window title
pub fn set_title(title: &str) -> Result<()> {
    core::with_engine_state(|state| {
        if let Some(ref window) = state.window {
            window.set_title(title);
            Ok(())
        } else {
            Err(EngineError::Window("No window created".to_string()))
        }
    })
}

/// Request window redraw
pub fn request_redraw() -> Result<()> {
    core::with_engine_state(|state| {
        if let Some(ref window) = state.window {
            window.request_redraw();
            Ok(())
        } else {
            Err(EngineError::Window("No window created".to_string()))
        }
    })
}
