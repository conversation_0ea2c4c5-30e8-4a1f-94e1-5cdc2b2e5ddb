use crate::{core, Result};
use std::collections::HashMap;
use winit::keyboard::Key;

/// Represents the state of a key
#[derive(Debug, <PERSON><PERSON>, <PERSON><PERSON>, PartialEq)]
pub enum KeyState {
    Released,
    Pressed,
    Held,
}

/// Action callback type
pub type ActionCallback = Box<dyn Fn() + Send + Sync>;

/// Input manager that tracks key states and actions
pub struct InputManager {
    /// Current key states
    key_states: HashMap<Key, KeyState>,
    /// Previous frame key states
    prev_key_states: HashMap<Key, KeyState>,
    /// Action name to keys mapping
    action_bindings: HashMap<String, Vec<Key>>,
    /// Action callbacks
    action_callbacks: HashMap<String, ActionCallback>,
}

impl InputManager {
    pub fn new() -> Self {
        Self {
            key_states: HashMap::new(),
            prev_key_states: HashMap::new(),
            action_bindings: HashMap::new(),
            action_callbacks: HashMap::new(),
        }
    }

    /// Handle keyboard input event from winit
    pub fn handle_keyboard_event(&mut self, event: &winit::event::KeyEvent) {
        let key = event.logical_key.clone();
        
        match event.state {
            winit::event::ElementState::Pressed => {
                // Check if this is a new press or a repeat
                let was_pressed = self.key_states.get(&key) == Some(&KeyState::Pressed) || 
                                 self.key_states.get(&key) == Some(&KeyState::Held);
                
                if was_pressed {
                    self.key_states.insert(key, KeyState::Held);
                } else {
                    self.key_states.insert(key, KeyState::Pressed);
                }
            }
            winit::event::ElementState::Released => {
                self.key_states.insert(key, KeyState::Released);
            }
        }
    }

    /// Update input state - should be called once per frame
    pub fn update(&mut self) {
        // Store previous frame states
        self.prev_key_states = self.key_states.clone();

        // Process action callbacks for pressed actions
        let pressed_actions: Vec<String> = self.action_bindings
            .iter()
            .filter(|(_, keys)| {
                keys.iter().any(|key| self.is_key_pressed_internal(key))
            })
            .map(|(action, _)| action.clone())
            .collect();

        for action in pressed_actions {
            if let Some(callback) = self.action_callbacks.get(&action) {
                callback();
            }
        }

        // Update key states: Pressed -> Held, Released -> (remove)
        let mut keys_to_remove = Vec::new();
        for (key, state) in self.key_states.iter_mut() {
            match state {
                KeyState::Pressed => *state = KeyState::Held,
                KeyState::Released => keys_to_remove.push(key.clone()),
                KeyState::Held => {} // Stay held
            }
        }

        // Remove released keys
        for key in keys_to_remove {
            self.key_states.remove(&key);
        }
    }

    /// Check if a key was just pressed this frame
    pub fn is_key_pressed(&self, key: &Key) -> bool {
        self.is_key_pressed_internal(key)
    }

    fn is_key_pressed_internal(&self, key: &Key) -> bool {
        self.key_states.get(key) == Some(&KeyState::Pressed)
    }

    /// Check if a key is currently held down
    pub fn is_key_held(&self, key: &Key) -> bool {
        matches!(self.key_states.get(key), Some(KeyState::Pressed) | Some(KeyState::Held))
    }

    /// Check if a key was just released this frame
    pub fn is_key_released(&self, key: &Key) -> bool {
        self.key_states.get(key) == Some(&KeyState::Released)
    }

    /// Create a new action
    pub fn create_action(&mut self, action_name: &str) {
        self.action_bindings.insert(action_name.to_string(), Vec::new());
    }

    /// Bind a key to an action
    pub fn bind_key(&mut self, action_name: &str, key: Key) {
        if let Some(keys) = self.action_bindings.get_mut(action_name) {
            if !keys.contains(&key) {
                keys.push(key);
            }
        } else {
            // Create action if it doesn't exist
            self.action_bindings.insert(action_name.to_string(), vec![key]);
        }
    }

    /// Unbind a key from an action
    pub fn unbind_key(&mut self, action_name: &str, key: &Key) {
        if let Some(keys) = self.action_bindings.get_mut(action_name) {
            keys.retain(|k| k != key);
        }
    }

    /// Check if an action was just pressed this frame
    pub fn is_action_pressed(&self, action_name: &str) -> bool {
        if let Some(keys) = self.action_bindings.get(action_name) {
            keys.iter().any(|key| self.is_key_pressed_internal(key))
        } else {
            false
        }
    }

    /// Check if an action is currently held down
    pub fn is_action_held(&self, action_name: &str) -> bool {
        if let Some(keys) = self.action_bindings.get(action_name) {
            keys.iter().any(|key| self.is_key_held(key))
        } else {
            false
        }
    }

    /// Check if an action was just released this frame
    pub fn is_action_released(&self, action_name: &str) -> bool {
        if let Some(keys) = self.action_bindings.get(action_name) {
            keys.iter().any(|key| self.is_key_released(key))
        } else {
            false
        }
    }

    /// Register a callback for when an action is pressed
    pub fn on_action_pressed(&mut self, action_name: &str, callback: ActionCallback) {
        self.action_callbacks.insert(action_name.to_string(), callback);
    }
}

// Public API functions that work with the global engine state

/// Initialize the input system
pub fn init() -> Result<()> {
    // Input system is initialized as part of EngineState::new()
    log::info!("Input system initialized");
    Ok(())
}

/// Update the input system (called internally by engine)
pub fn update() -> Result<()> {
    core::with_engine_state(|state| {
        state.input_manager.update();
        Ok(())
    })
}

/// Shutdown the input system
pub fn shutdown() -> Result<()> {
    log::info!("Input system shutdown");
    Ok(())
}

/// Check if a key was just pressed this frame
pub fn is_key_pressed(key: Key) -> bool {
    core::with_engine_state(|state| {
        Ok(state.input_manager.is_key_pressed(&key))
    }).unwrap_or(false)
}

/// Check if a key is currently held down
pub fn is_key_held(key: Key) -> bool {
    core::with_engine_state(|state| {
        Ok(state.input_manager.is_key_held(&key))
    }).unwrap_or(false)
}

/// Check if a key was just released this frame
pub fn is_key_released(key: Key) -> bool {
    core::with_engine_state(|state| {
        Ok(state.input_manager.is_key_released(&key))
    }).unwrap_or(false)
}

/// Create a new action
pub fn create_action(action_name: &str) -> Result<()> {
    core::with_engine_state(|state| {
        state.input_manager.create_action(action_name);
        Ok(())
    })
}

/// Bind a key to an action
pub fn bind_key(action_name: &str, key: Key) -> Result<()> {
    core::with_engine_state(|state| {
        state.input_manager.bind_key(action_name, key);
        Ok(())
    })
}

/// Unbind a key from an action
pub fn unbind_key(action_name: &str, key: Key) -> Result<()> {
    core::with_engine_state(|state| {
        state.input_manager.unbind_key(action_name, &key);
        Ok(())
    })
}

/// Check if an action was just pressed this frame
pub fn is_action_pressed(action_name: &str) -> bool {
    core::with_engine_state(|state| {
        Ok(state.input_manager.is_action_pressed(action_name))
    }).unwrap_or(false)
}

/// Check if an action is currently held down
pub fn is_action_held(action_name: &str) -> bool {
    core::with_engine_state(|state| {
        Ok(state.input_manager.is_action_held(action_name))
    }).unwrap_or(false)
}

/// Check if an action was just released this frame
pub fn is_action_released(action_name: &str) -> bool {
    core::with_engine_state(|state| {
        Ok(state.input_manager.is_action_released(action_name))
    }).unwrap_or(false)
}

/// Register a callback for when an action is pressed
pub fn on_action_pressed<F>(action_name: &str, callback: F) -> Result<()>
where
    F: Fn() + Send + Sync + 'static,
{
    core::with_engine_state(|state| {
        state.input_manager.on_action_pressed(action_name, Box::new(callback));
        Ok(())
    })
}

/// Handle keyboard event (internal function called by engine)
pub(crate) fn handle_keyboard_event(event: &winit::event::KeyEvent) -> Result<()> {
    core::with_engine_state(|state| {
        state.input_manager.handle_keyboard_event(event);
        Ok(())
    })
}
