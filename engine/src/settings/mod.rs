use crate::{core, <PERSON><PERSON>rror, Result};
use serde::{Deserialize, Serialize};

#[derive(Debug, <PERSON><PERSON>, <PERSON><PERSON>, PartialEq, Eq, Serialize, Deserialize)]
pub enum VsyncType {
    Off,
    On,
    Adaptive,
}

#[derive(Debug, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>ialEq, Eq, Serialize, Deserialize)]
pub enum FullscreenType {
    Windowed,
    Borderless,
    Exclusive,
}

#[derive(Debu<PERSON>, <PERSON><PERSON>, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum FpsLimit {
    Uncapped,
    Fps30,
    Fps60,
    Fps120,
    Custom(u32),
}

#[derive(Debug, <PERSON>lone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum MsaaType {
    Off,    // 1x - No MSAA
    X2,     // 2x MSAA
    X4,     // 4x MSAA
    X8,     // 8x MSAA
}

impl MsaaType {
    /// Get the sample count for this MSAA type
    pub fn sample_count(&self) -> u32 {
        match self {
            MsaaType::Off => 1,
            MsaaType::X2 => 2,
            MsaaType::X4 => 4,
            MsaaType::X8 => 8,
        }
    }
}

#[derive(Debu<PERSON>, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct Settings {
    pub vsync: VsyncType,
    pub fullscreen: FullscreenType,
    pub fps_limit: FpsLimit,
    pub msaa: MsaaType,
    pub resolution_x: u32,
    pub resolution_y: u32,
}

impl Default for Settings {
    fn default() -> Self {
        Self {
            vsync: VsyncType::On,
            fullscreen: FullscreenType::Windowed,
            fps_limit: FpsLimit::Fps60,
            msaa: MsaaType::Off,
            resolution_x: 1280,
            resolution_y: 720,
        }
    }
}

impl Settings {
    /// Load settings from config file, or create default if not found
    pub fn load() -> Self {
        match std::fs::read_to_string("engine_config.toml") {
            Ok(content) => {
                match toml::from_str(&content) {
                    Ok(settings) => {
                        log::info!("Loaded settings from config file");
                        settings
                    }
                    Err(e) => {
                        log::warn!("Failed to parse config file: {}, using defaults", e);
                        Self::default()
                    }
                }
            }
            Err(_) => {
                log::info!("No config file found, using default settings");
                Self::default()
            }
        }
    }

    /// Save settings to config file
    pub fn save(&self) -> Result<()> {
        let content = toml::to_string_pretty(self)
            .map_err(|e| EngineError::Other(anyhow::anyhow!("Failed to serialize settings: {}", e)))?;
        
        std::fs::write("engine_config.toml", content)?;
        log::info!("Settings saved to config file");
        Ok(())
    }

    /// Apply settings to the engine (update surface config, etc.)
    pub fn apply(&self) -> Result<()> {
        core::with_engine_state(|state| {
            let msaa_changed = state.settings.msaa != self.msaa;

            // Update surface configuration if we have a surface
            if let (Some(surface), Some(ref mut config)) = (&state.surface, &mut state.surface_config) {
                // Update present mode based on vsync setting
                config.present_mode = match self.vsync {
                    VsyncType::Off => wgpu::PresentMode::Immediate,
                    VsyncType::On => wgpu::PresentMode::Fifo,
                    VsyncType::Adaptive => wgpu::PresentMode::FifoRelaxed,
                };

                // Reconfigure surface
                surface.configure(&state.device, config);

                // If MSAA changed, recreate MSAA textures and pipelines
                if msaa_changed {
                    let requested_sample_count = self.msaa.sample_count();
                    let surface_format = config.format;

                    // Recreate MSAA texture and get validated sample count
                    let validated_sample_count = state.renderer.create_msaa_texture(&state.device, config.width, config.height, requested_sample_count, surface_format)?;

                    // Recreate pipelines with validated sample count
                    state.renderer.create_pipeline(&state.device, surface_format, validated_sample_count)?;
                    state.renderer.create_text_pipeline(&state.device, surface_format, validated_sample_count)?;

                    log::info!("Recreated render pipelines for MSAA: {}x", validated_sample_count);
                }

                log::info!("Applied settings to surface configuration");
            }

            // Update internal settings
            state.settings = self.clone();
            Ok(())
        })
    }
}

/// Set engine settings
pub fn set(
    vsync_type: VsyncType,
    fullscreen_type: FullscreenType,
    fps_limit: FpsLimit,
    msaa_type: MsaaType,
    res_x: u32,
    res_y: u32,
) -> Result<()> {
    let settings = Settings {
        vsync: vsync_type,
        fullscreen: fullscreen_type,
        fps_limit,
        msaa: msaa_type,
        resolution_x: res_x,
        resolution_y: res_y,
    };

    settings.apply()?;
    settings.save()?;
    
    log::info!("Settings updated: {:?}", settings);
    Ok(())
}

/// Get current engine settings
pub fn get() -> Result<Settings> {
    core::with_engine_state(|state| Ok(state.settings.clone()))
}
