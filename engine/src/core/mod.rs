use crate::{EngineState, EngineError, Result};

/// Initialize the engine with wgpu device, surface, and all subsystems
pub fn init() -> Result<()> {
    env_logger::init();
    log::info!("Initializing Simple WGPU Engine");

    let engine_state = EngineState::new()?;
    
    let state_mutex = EngineState::get_global();
    let mut state_guard = state_mutex.lock().unwrap();
    *state_guard = Some(engine_state);
    
    log::info!("Engine initialized successfully");
    Ok(())
}

/// Check if the engine is initialized
pub fn is_initialized() -> bool {
    let state_mutex = EngineState::get_global();
    let state_guard = state_mutex.lock().unwrap();
    state_guard.is_some()
}

/// Get a reference to the engine state (for internal use)
pub(crate) fn with_engine_state<F, R>(f: F) -> Result<R>
where
    F: FnOnce(&mut EngineState) -> Result<R>,
{
    let state_mutex = EngineState::get_global();
    let mut state_guard = state_mutex.lock().unwrap();
    
    match state_guard.as_mut() {
        Some(state) => f(state),
        None => Err(EngineError::NotInitialized),
    }
}

/// Run the game loop with initialization and render callbacks
pub fn run<I, R>(init_callback: I, mut render_callback: R) -> Result<()>
where
    I: FnOnce() -> Result<()> + 'static,
    R: FnMut(u32) -> Result<()> + 'static,
{
    use winit::{
        event::{Event, WindowEvent},
        event_loop::{ControlFlow, EventLoop},
    };

    // Create event loop
    let event_loop = EventLoop::new()
        .map_err(|e| EngineError::Window(format!("Failed to create event loop: {}", e)))?;

    let mut frame_count = 0;
    let mut window_created = false;
    let mut init_callback = Some(init_callback);

    // Run event loop
    event_loop.run(move |event, elwt| {
        elwt.set_control_flow(ControlFlow::Poll);

        // Create window on first iteration
        if !window_created {
            if let Err(e) = crate::window::new_with_event_loop("Game Window", 1, elwt) {
                eprintln!("Failed to create window: {}", e);
                elwt.exit();
                return;
            }

            // Call initialization callback after window is created
            if let Some(callback) = init_callback.take() {
                if let Err(e) = callback() {
                    eprintln!("Initialization error: {}", e);
                    elwt.exit();
                    return;
                }
            }

            window_created = true;
        }

        match event {
            Event::WindowEvent { event, .. } => match event {
                WindowEvent::CloseRequested => {
                    println!("Window close requested");
                    elwt.exit();
                }
                WindowEvent::KeyboardInput { event, .. } => {
                    // Pass keyboard event to input system
                    if let Err(e) = crate::input::handle_keyboard_event(&event) {
                        eprintln!("Input system error: {}", e);
                    }
                }
                WindowEvent::Resized(physical_size) => {
                    if let Err(e) = crate::window::handle_resize(physical_size.width, physical_size.height) {
                        eprintln!("Failed to handle resize: {}", e);
                    }
                }
                WindowEvent::RedrawRequested => {
                    // Update input system before rendering
                    if let Err(e) = crate::input::update() {
                        eprintln!("Input update error: {}", e);
                    }

                    // Call the render callback
                    if let Err(e) = render_callback(frame_count) {
                        eprintln!("Render error: {}", e);
                        elwt.exit();
                    }
                    frame_count += 1;
                }
                _ => {}
            },
            Event::AboutToWait => {
                // Request redraw
                if let Err(e) = crate::window::request_redraw() {
                    eprintln!("Failed to request redraw: {}", e);
                }
            }
            _ => {}
        }
    }).map_err(|e| EngineError::Window(format!("Event loop error: {}", e)))
}

/// Shutdown the engine and clean up resources
pub fn shutdown() -> Result<()> {
    log::info!("Shutting down engine");

    let state_mutex = EngineState::get_global();
    let mut state_guard = state_mutex.lock().unwrap();
    *state_guard = None;

    log::info!("Engine shutdown complete");
    Ok(())
}
