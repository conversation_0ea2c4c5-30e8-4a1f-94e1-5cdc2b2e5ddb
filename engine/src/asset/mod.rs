use crate::{core, EngineError, Result};
use std::collections::HashMap;
use std::path::Path;
use image::GenericImageView;

pub struct Texture {
    pub texture: wgpu::Texture,
    pub view: wgpu::TextureView,
    pub sampler: wgpu::Sampler,
    pub width: u32,
    pub height: u32,
}

pub struct Font {
    pub font: fontdue::Font,
    pub atlas: Option<TextureAtlas>,
}

pub struct TextureAtlas {
    pub texture: wgpu::Texture,
    pub view: wgpu::TextureView,
    pub width: u32,
    pub height: u32,
    pub entries: HashMap<char, AtlasEntry>,
}

pub struct AtlasEntry {
    pub x: u32,
    pub y: u32,
    pub width: u32,
    pub height: u32,
    pub advance_width: f32,
}

pub struct AssetManager {
    pub sprites: HashMap<String, Texture>,
    pub fonts: HashMap<String, Font>,
}

impl AssetManager {
    pub fn new() -> Self {
        Self {
            sprites: HashMap::new(),
            fonts: HashMap::new(),
        }
    }

    pub fn load_sprite_from_path(&mut self, device: &wgpu::Device, queue: &wgpu::Queue, path: &Path) -> Result<Texture> {
        let img = image::open(path)
            .map_err(|e| EngineError::Asset(format!("Failed to load image {}: {}", path.display(), e)))?;
        
        let rgba = img.to_rgba8();
        let dimensions = img.dimensions();

        let texture_size = wgpu::Extent3d {
            width: dimensions.0,
            height: dimensions.1,
            depth_or_array_layers: 1,
        };

        let texture = device.create_texture(&wgpu::TextureDescriptor {
            size: texture_size,
            mip_level_count: 1,
            sample_count: 1,
            dimension: wgpu::TextureDimension::D2,
            format: wgpu::TextureFormat::Rgba8UnormSrgb,
            usage: wgpu::TextureUsages::TEXTURE_BINDING | wgpu::TextureUsages::COPY_DST,
            label: Some(&format!("texture_{}", path.display())),
            view_formats: &[],
        });

        queue.write_texture(
            wgpu::ImageCopyTexture {
                texture: &texture,
                mip_level: 0,
                origin: wgpu::Origin3d::ZERO,
                aspect: wgpu::TextureAspect::All,
            },
            &rgba,
            wgpu::ImageDataLayout {
                offset: 0,
                bytes_per_row: Some(4 * dimensions.0),
                rows_per_image: Some(dimensions.1),
            },
            texture_size,
        );

        let view = texture.create_view(&wgpu::TextureViewDescriptor::default());
        let sampler = device.create_sampler(&wgpu::SamplerDescriptor {
            address_mode_u: wgpu::AddressMode::ClampToEdge,
            address_mode_v: wgpu::AddressMode::ClampToEdge,
            address_mode_w: wgpu::AddressMode::ClampToEdge,
            mag_filter: wgpu::FilterMode::Nearest,
            min_filter: wgpu::FilterMode::Nearest,
            mipmap_filter: wgpu::FilterMode::Nearest,
            ..Default::default()
        });

        Ok(Texture {
            texture,
            view,
            sampler,
            width: dimensions.0,
            height: dimensions.1,
        })
    }

    pub fn load_font_from_path(&mut self, device: &wgpu::Device, queue: &wgpu::Queue, path: &Path) -> Result<Font> {
        let font_data = std::fs::read(path)
            .map_err(|e| EngineError::Asset(format!("Failed to read font file {}: {}", path.display(), e)))?;

        let font = fontdue::Font::from_bytes(font_data, fontdue::FontSettings::default())
            .map_err(|e| EngineError::Asset(format!("Failed to parse font {}: {}", path.display(), e)))?;

        // Generate font atlas
        let atlas = self.generate_font_atlas(device, queue, &font)?;

        Ok(Font {
            font,
            atlas: Some(atlas),
        })
    }

    fn generate_font_atlas(&mut self, device: &wgpu::Device, queue: &wgpu::Queue, font: &fontdue::Font) -> Result<TextureAtlas> {
        use rectangle_pack::{contains_smallest_box, pack_rects, volume_heuristic, GroupedRectsToPlace, RectToInsert, TargetBin};

        const FONT_SIZE: f32 = 48.0;
        const ATLAS_SIZE: u32 = 512;

        // Characters to include in atlas (printable ASCII)
        let chars: Vec<char> = (32..127).map(|i| i as u8 as char).collect();

        // Rasterize all characters and collect their data
        let mut glyph_data = Vec::new();
        let mut rects_to_place: GroupedRectsToPlace<usize, ()> = GroupedRectsToPlace::new();

        for (i, &ch) in chars.iter().enumerate() {
            let (metrics, bitmap) = font.rasterize(ch, FONT_SIZE);

            if metrics.width > 0 && metrics.height > 0 {
                // Add padding around glyphs
                let padded_width = metrics.width + 2;
                let padded_height = metrics.height + 2;

                rects_to_place.push_rect(
                    i,
                    None,
                    RectToInsert::new(padded_width as u32, padded_height as u32, 1)
                );

                glyph_data.push((ch, metrics, bitmap));
            } else {
                // For space and other zero-width characters
                glyph_data.push((ch, metrics, bitmap));
            }
        }

        // Pack rectangles into atlas
        let mut target_bins = std::collections::BTreeMap::new();
        target_bins.insert(0, TargetBin::new(ATLAS_SIZE, ATLAS_SIZE, 1));

        let rectangle_placements = pack_rects(
            &rects_to_place,
            &mut target_bins,
            &volume_heuristic,
            &contains_smallest_box,
        ).map_err(|e| EngineError::Asset(format!("Failed to pack font atlas: {:?}", e)))?;

        // Create atlas texture
        let mut atlas_data = vec![0u8; (ATLAS_SIZE * ATLAS_SIZE) as usize];
        let mut entries = HashMap::new();

        // Place glyphs in atlas
        for (glyph_idx, (ch, metrics, bitmap)) in glyph_data.iter().enumerate() {
            if metrics.width > 0 && metrics.height > 0 {
                if let Some((_, placement)) = rectangle_placements.packed_locations().get(&glyph_idx) {
                    let x = placement.x() + 1; // Account for padding
                    let y = placement.y() + 1;

                    // Copy glyph bitmap to atlas
                    for row in 0..metrics.height {
                        for col in 0..metrics.width {
                            let src_idx = row * metrics.width + col;
                            let dst_idx = ((y + row as u32) * ATLAS_SIZE + (x + col as u32)) as usize;
                            if src_idx < bitmap.len() && dst_idx < atlas_data.len() {
                                atlas_data[dst_idx] = bitmap[src_idx];
                            }
                        }
                    }

                    entries.insert(*ch, AtlasEntry {
                        x: x as u32,
                        y: y as u32,
                        width: metrics.width as u32,
                        height: metrics.height as u32,
                        advance_width: metrics.advance_width,
                    });
                }
            } else {
                // For space and other zero-width characters
                entries.insert(*ch, AtlasEntry {
                    x: 0,
                    y: 0,
                    width: 0,
                    height: 0,
                    advance_width: metrics.advance_width,
                });
            }
        }

        // Create GPU texture
        let texture = device.create_texture(&wgpu::TextureDescriptor {
            size: wgpu::Extent3d {
                width: ATLAS_SIZE,
                height: ATLAS_SIZE,
                depth_or_array_layers: 1,
            },
            mip_level_count: 1,
            sample_count: 1,
            dimension: wgpu::TextureDimension::D2,
            format: wgpu::TextureFormat::R8Unorm, // Single channel for font atlas
            usage: wgpu::TextureUsages::TEXTURE_BINDING | wgpu::TextureUsages::COPY_DST,
            label: Some("font_atlas"),
            view_formats: &[],
        });

        queue.write_texture(
            wgpu::ImageCopyTexture {
                texture: &texture,
                mip_level: 0,
                origin: wgpu::Origin3d::ZERO,
                aspect: wgpu::TextureAspect::All,
            },
            &atlas_data,
            wgpu::ImageDataLayout {
                offset: 0,
                bytes_per_row: Some(ATLAS_SIZE),
                rows_per_image: Some(ATLAS_SIZE),
            },
            wgpu::Extent3d {
                width: ATLAS_SIZE,
                height: ATLAS_SIZE,
                depth_or_array_layers: 1,
            },
        );

        let view = texture.create_view(&wgpu::TextureViewDescriptor::default());

        Ok(TextureAtlas {
            texture,
            view,
            width: ATLAS_SIZE,
            height: ATLAS_SIZE,
            entries,
        })
    }
}

/// Load sprite from assets folder
pub fn load_sprite(filename: &str, sprite_name: &str) -> Result<()> {
    let assets_path = Path::new("assets/sprites").join(filename);
    
    if !assets_path.exists() {
        return Err(EngineError::Asset(format!("Sprite file not found: {}", assets_path.display())));
    }

    core::with_engine_state(|state| {
        let texture = state.asset_manager.load_sprite_from_path(&state.device, &state.queue, &assets_path)?;

        // Create texture bind group for rendering
        state.renderer.create_texture_bind_group(&state.device, &texture.texture, sprite_name)?;

        state.asset_manager.sprites.insert(sprite_name.to_string(), texture);

        log::info!("Loaded sprite '{}' from {}", sprite_name, filename);
        Ok(())
    })
}

/// Load TTF font from assets folder
pub fn load_font(filename: &str, font_name: &str) -> Result<()> {
    let assets_path = Path::new("assets").join(filename);
    
    if !assets_path.exists() {
        return Err(EngineError::Asset(format!("Font file not found: {}", assets_path.display())));
    }

    core::with_engine_state(|state| {
        let font = state.asset_manager.load_font_from_path(&state.device, &state.queue, &assets_path)?;

        // Create font bind group for rendering
        if let Some(ref atlas) = font.atlas {
            state.renderer.create_font_bind_group(&state.device, &atlas.view, font_name)?;
        }

        state.asset_manager.fonts.insert(font_name.to_string(), font);

        log::info!("Loaded font '{}' from {}", font_name, filename);
        Ok(())
    })
}

/// Check if sprite exists
pub fn has_sprite(sprite_name: &str) -> Result<bool> {
    core::with_engine_state(|state| {
        Ok(state.asset_manager.sprites.contains_key(sprite_name))
    })
}

/// Check if font exists
pub fn has_font(font_name: &str) -> Result<bool> {
    core::with_engine_state(|state| {
        Ok(state.asset_manager.fonts.contains_key(font_name))
    })
}
