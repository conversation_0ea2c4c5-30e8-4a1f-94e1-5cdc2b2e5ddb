use glam::{Mat4, Vec3};

/// Create a transformation matrix for 2D sprites
pub fn create_transform_matrix(
    x: f32,
    y: f32,
    z: f32,
    scale: f32,
    rotation: f32,
    _screen_width: f32,
    _screen_height: f32,
) -> Mat4 {
    // Convert normalized coordinates (0.0-1.0) to NDC (-1.0 to 1.0)
    let ndc_x = (x * 2.0) - 1.0;
    let ndc_y = -((y * 2.0) - 1.0); // Flip Y axis (0,0 = top-left)
    let ndc_z = z; // Z is already in 0.0-1.0 range
    
    // Create transformation matrix
    let translation = Mat4::from_translation(Vec3::new(ndc_x, ndc_y, ndc_z));
    let rotation = Mat4::from_rotation_z(rotation);
    let scale = Mat4::from_scale(Vec3::new(scale, scale, 1.0));
    
    translation * rotation * scale
}

/// Convert normalized coordinates to screen coordinates
pub fn normalized_to_screen(x: f32, y: f32, screen_width: f32, screen_height: f32) -> (f32, f32) {
    (x * screen_width, y * screen_height)
}

/// Convert screen coordinates to normalized coordinates
pub fn screen_to_normalized(x: f32, y: f32, screen_width: f32, screen_height: f32) -> (f32, f32) {
    (x / screen_width, y / screen_height)
}

/// Calculate aspect ratio preserving scale
pub fn calculate_aspect_scale(
    target_width: f32,
    target_height: f32,
    actual_width: f32,
    actual_height: f32,
) -> (f32, f32) {
    let target_aspect = target_width / target_height;
    let actual_aspect = actual_width / actual_height;
    
    if actual_aspect > target_aspect {
        // Window is wider than target, scale by height
        let scale = actual_height / target_height;
        (scale, scale)
    } else {
        // Window is taller than target, scale by width
        let scale = actual_width / target_width;
        (scale, scale)
    }
}

/// Clamp value between min and max
pub fn clamp(value: f32, min: f32, max: f32) -> f32 {
    if value < min {
        min
    } else if value > max {
        max
    } else {
        value
    }
}

/// Linear interpolation
pub fn lerp(a: f32, b: f32, t: f32) -> f32 {
    a + (b - a) * t
}

/// Check if point is inside rectangle (normalized coordinates)
pub fn point_in_rect(px: f32, py: f32, rx: f32, ry: f32, rw: f32, rh: f32) -> bool {
    px >= rx && px <= rx + rw && py >= ry && py <= ry + rh
}
