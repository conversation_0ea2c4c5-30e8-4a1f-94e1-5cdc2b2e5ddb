// Text rendering shader

struct VertexInput {
    @location(0) position: vec3<f32>,
    @location(1) tex_coords: vec2<f32>,
}

struct TextInstance {
    @location(2) transform_0: vec4<f32>,
    @location(3) transform_1: vec4<f32>,
    @location(4) transform_2: vec4<f32>,
    @location(5) transform_3: vec4<f32>,
    @location(6) atlas_coords: vec4<f32>, // x, y, width, height in font atlas (normalized)
    @location(7) color: vec4<f32>,
}

struct VertexOutput {
    @builtin(position) clip_position: vec4<f32>,
    @location(0) tex_coords: vec2<f32>,
    @location(1) color: vec4<f32>,
}

struct ScreenUniforms {
    screen_size: vec2<f32>,
    aspect_ratio: f32,
    _padding: f32,
}

@group(1) @binding(0)
var<uniform> screen: ScreenUniforms;

@vertex
fn vs_main(
    vertex: VertexInput,
    instance: TextInstance,
) -> VertexOutput {
    var out: VertexOutput;

    // Transform matrix from instance
    let transform = mat4x4<f32>(
        instance.transform_0,
        instance.transform_1,
        instance.transform_2,
        instance.transform_3,
    );

    // Extract position and scale from transform matrix
    let pos_x = instance.transform_0.w;
    let pos_y = instance.transform_1.w;
    let scale_x = instance.transform_0.x;
    let scale_y = instance.transform_1.y;

    // Apply transformation similar to sprite shader
    let scaled_vertex = vertex.position.xy * vec2<f32>(scale_x, scale_y);
    let world_pos = scaled_vertex + vec2<f32>(pos_x, pos_y);

    // Convert to normalized device coordinates (NDC)
    // The coordinate system is now 0,0 top-left, 1,1 bottom-right
    // Apply 4:3 aspect ratio correction with letterboxing
    let target_aspect = 4.0 / 3.0;
    let screen_aspect = screen.aspect_ratio;

    var corrected_pos = world_pos;

    if (screen_aspect > target_aspect) {
        // Screen is wider than 4:3, add letterboxing on sides
        let scale_factor = target_aspect / screen_aspect;
        corrected_pos.x = (corrected_pos.x - 0.5) * scale_factor + 0.5;
    } else {
        // Screen is taller than 4:3, add letterboxing on top/bottom
        let scale_factor = screen_aspect / target_aspect;
        corrected_pos.y = (corrected_pos.y - 0.5) * scale_factor + 0.5;
    }

    let final_pos = vec4<f32>(
        (corrected_pos.x * 2.0) - 1.0,
        (corrected_pos.y * -2.0) + 1.0, // Flip Y axis
        vertex.position.z,
        1.0
    );

    out.clip_position = final_pos;
    
    // Map vertex texture coordinates to atlas coordinates
    let atlas_uv = instance.atlas_coords.xy + vertex.tex_coords * instance.atlas_coords.zw;
    out.tex_coords = atlas_uv;
    out.color = instance.color;

    return out;
}

// Fragment shader

@group(0) @binding(0)
var t_font_atlas: texture_2d<f32>;
@group(0) @binding(1)
var s_font_atlas: sampler;

@fragment
fn fs_main(in: VertexOutput) -> @location(0) vec4<f32> {
    let alpha = textureSample(t_font_atlas, s_font_atlas, in.tex_coords).r;
    return vec4<f32>(in.color.rgb, in.color.a * alpha);
}