pub mod core;
pub mod window;
pub mod settings;
pub mod asset;
pub mod render;
pub mod utils;
pub mod input;

// Re-export main API
pub use core::*;
pub use window::*;
pub use settings::*;
pub use asset::*;
pub use render::*;
// Re-export input functions but avoid conflicts with core module
pub use input::{
    is_key_pressed, is_key_held, is_key_released,
    create_action, bind_key, unbind_key,
    is_action_pressed, is_action_held, is_action_released,
    on_action_pressed
};
// Re-export key types for convenience
pub use winit::keyboard::{Key, NamedKey};

use std::sync::OnceLock;
use std::sync::Mutex;

// Global engine state
static ENGINE_STATE: OnceLock<Mutex<Option<EngineState>>> = OnceLock::new();

pub struct EngineState {
    pub instance: wgpu::Instance,
    pub adapter: wgpu::Adapter,
    pub device: wgpu::Device,
    pub queue: wgpu::Queue,
    pub surface: Option<wgpu::Surface<'static>>,
    pub surface_config: Option<wgpu::SurfaceConfiguration>,
    pub window: Option<std::sync::Arc<winit::window::Window>>,
    pub settings: settings::Settings,
    pub asset_manager: asset::AssetManager,
    pub renderer: render::Renderer,
    pub input_manager: input::InputManager,
    pub last_frame_time: std::time::Instant,
    pub delta_time: f32,
    pub fps: f32,
    pub frame_count: u32,
    pub fps_timer: std::time::Instant,
}

impl EngineState {
    pub fn new() -> anyhow::Result<Self> {
        // Initialize wgpu
        let instance = wgpu::Instance::new(wgpu::InstanceDescriptor {
            backends: wgpu::Backends::all(),
            ..Default::default()
        });

        let adapter = pollster::block_on(instance.request_adapter(&wgpu::RequestAdapterOptions {
            power_preference: wgpu::PowerPreference::default(),
            compatible_surface: None,
            force_fallback_adapter: false,
        }))
        .ok_or_else(|| anyhow::anyhow!("Failed to find an appropriate adapter"))?;

        let (device, queue) = pollster::block_on(adapter.request_device(
            &wgpu::DeviceDescriptor {
                required_features: wgpu::Features::empty(),
                required_limits: wgpu::Limits::default(),
                label: None,
            },
            None,
        ))?;

        let settings = settings::Settings::default();
        let asset_manager = asset::AssetManager::new();
        let renderer = render::Renderer::new(&device, &queue)?;
        let input_manager = input::InputManager::new();

        let now = std::time::Instant::now();

        Ok(Self {
            instance,
            adapter,
            device,
            queue,
            surface: None,
            surface_config: None,
            window: None,
            settings,
            asset_manager,
            renderer,
            input_manager,
            last_frame_time: now,
            delta_time: 0.0,
            fps: 0.0,
            frame_count: 0,
            fps_timer: now,
        })
    }

    pub fn get_global() -> &'static Mutex<Option<EngineState>> {
        ENGINE_STATE.get_or_init(|| Mutex::new(None))
    }
}

#[derive(Debug, thiserror::Error)]
pub enum EngineError {
    #[error("Engine not initialized")]
    NotInitialized,
    #[error("WGPU error: {0}")]
    Wgpu(#[from] wgpu::RequestDeviceError),
    #[error("Window error: {0}")]
    Window(String),
    #[error("Asset error: {0}")]
    Asset(String),
    #[error("Render error: {0}")]
    Render(String),
    #[error("IO error: {0}")]
    Io(#[from] std::io::Error),
    #[error("Other error: {0}")]
    Other(#[from] anyhow::Error),
}

pub type Result<T> = std::result::Result<T, EngineError>;
